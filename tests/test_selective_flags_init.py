import pytest
from ffs_lib_py import FeatureFlagSDK
from fixtures.sdk_fixtures import setup_env_vars

USER_ID_WITH_ACCESS = 4

@pytest.mark.asyncio
async def test_init_with_selective_flags(setup_env_vars):

    sdk = FeatureFlagSDK.get_instance()
    await sdk.init(interval_in_minutes=1, flag_names=["FFS_NETOMI", "IS_CONTEXT_MANAGEMENT_ENABLED", "IS_ANALYTICS_LITE"])

    evaluate_flags = sdk.get_flags({"userId": USER_ID_WITH_ACCESS})

    assert evaluate_flags is not None
    assert isinstance(evaluate_flags, dict)
    assert len(evaluate_flags) == 3 and "FFS_NETOMI" in evaluate_flags and "IS_CONTEXT_MANAGEMENT_ENABLED" in evaluate_flags and "IS_ANALYTICS_LITE" in evaluate_flags
    print("Flag Value:", evaluate_flags)



