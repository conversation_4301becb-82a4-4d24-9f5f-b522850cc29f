import pytest
from ffs_lib_py import FeatureFlagSDK
from fixtures.sdk_fixtures import setup_env_vars
import os

# Constants
FLAG_NAME = "FFS_NETOMI"
USER_ID_WITH_ACCESS = 4
USER_ID_WITHOUT_ACCESS = 3


@pytest.mark.asyncio
async def test_init(setup_env_vars):
    # print("CONFIG_HOST:", os.getenv("CONFIG_HOST"))

    sdk = FeatureFlagSDK.get_instance()
    await sdk.init(interval_in_minutes=1)
    assert "token" in sdk.ffs_config
    assert isinstance(sdk.ffs_config["token"], str)
    # print("Token:", sdk.ffs_config["token"])

@pytest.mark.asyncio
async def test_get_flags(setup_env_vars):

    sdk = FeatureFlagSDK.get_instance()
    await sdk.init(interval_in_minutes=1)

    evaluated_flags = sdk.get_flags({"userId": USER_ID_WITH_ACCESS})

    assert evaluated_flags is not None
    assert isinstance(evaluated_flags, dict)
    assert FLAG_NAME in evaluated_flags 
    # print("get flag/ Flag Value:", evaluated_flags)

@pytest.mark.asyncio
async def test_get_flag_value(setup_env_vars):

    sdk = FeatureFlagSDK.get_instance()
    await sdk.init(interval_in_minutes=1)

    flag_value_with_access = sdk.get_flag_value(FLAG_NAME, {"userId": USER_ID_WITH_ACCESS})
    flag_value_without_access = sdk.get_flag_value(FLAG_NAME, {"userId": USER_ID_WITHOUT_ACCESS})
    
    assert flag_value_with_access is not None
    assert flag_value_with_access == True 
    print("flag_value_with_access:", flag_value_with_access)

    assert flag_value_without_access is not None
    assert flag_value_without_access == False
    print("flag_value_without_access:", flag_value_without_access)

def test_init_sync(setup_env_vars):

    sdk = FeatureFlagSDK.get_instance()
    sdk.init_sync(interval_in_minutes=1)

    assert "token" in sdk.ffs_config
    assert isinstance(sdk.ffs_config["token"], str)
    # print("Token (sync):", sdk.ffs_config["token"])

def test_sdk_singleton_behavior(setup_env_vars):
    sdk1 = FeatureFlagSDK.get_instance()
    sdk2 = FeatureFlagSDK.get_instance()
    assert sdk1 is sdk2

