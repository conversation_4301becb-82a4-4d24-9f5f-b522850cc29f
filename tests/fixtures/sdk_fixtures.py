import os
import pytest

@pytest.fixture(scope="function")
def setup_env_vars():
    os.environ["CONFIG_HOST"] = "http://configmanager-dev1.internal.netomi.com/v1/service/configuration/get"
    os.environ["CONFIG_REGION"] = "us-east-1"
    os.environ["CONFIG_ENV"] = "dev1"
    yield
    # Optionally clear them after test
    os.environ.pop("CONFIG_HOST", None)
    os.environ.pop("CONFIG_REGION", None)
    os.environ.pop("CONFIG_ENV", None)
