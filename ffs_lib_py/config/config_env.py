import os
import asyncio
from typing import Any, Dict, Optional
# from dotenv import load_dotenv
from ffs_lib_py.utils.ffs_http import FFSHttpClient
from ffs_lib_py.utils import log


class ConfigEnv:
    _instance: Optional["ConfigEnv"] = None

    def __init__(self):
        self._config_map: Dict[str, Any] = {}
        self._initialized: bool = False
        self._initializing: Optional[asyncio.Future] = None

    @classmethod
    def get_instance(cls) -> "ConfigEnv":
        if cls._instance is None:
            cls._instance = ConfigEnv()
        return cls._instance

    async def init(self) -> None:
        if self._initialized:
            log.debug("ConfigEnv: Already initialized, skipping")
            return

        if self._initializing:
            log.debug("ConfigEnv: Initialization in progress, waiting")
            await self._initializing
            return

        log.info("ConfigEnv: Starting initialization")
        loop = asyncio.get_running_loop()
        self._initializing = loop.create_task(self._load_config())

        try:
            await self._initializing
            self._initialized = True
            log.info("ConfigEnv: Initialization complete")
        except Exception as error:
            log.error("ConfigEnv: Initialization failed", error)
            raise
        finally:
            self._initializing = None

    async def _load_config(self) -> None:
        from dotenv import load_dotenv
        load_dotenv()

        # Read after .env is loaded
        CONFIG_SERVICE_NAME = "ffs-lib"
        config_host = os.getenv("CONFIG_HOST") or os.getenv("CONFIGMANAGER_URL") or os.getenv("CONFIG_HOSTNAME") or os.getenv("CONFIG_HOST_NAME") or ""
        config_region = os.getenv("CONFIG_REGION") or os.getenv("APP_REGION") or os.getenv("ENV_REGION") or os.getenv("AWS_REGION") or os.getenv("REGION") or ""
        config_env = os.getenv("CONFIG_ENV") or os.getenv("ENV_CONFIG") or os.getenv("APP_ENV") or os.getenv("ENV_APP") or os.getenv("SERVICE_ENV") or os.getenv("ENVIRONMENT") or os.getenv("ENV") or ""

        log.info(f"ConfigEnv: Loading config from {config_host}, {config_region}, {config_env}")
        try:
            http = FFSHttpClient(base_url=config_host)
            response = await http.post({
                "url": "",
                "bypass_auth": True,
                "body": {
                    "serviceName": CONFIG_SERVICE_NAME,
                    "region": config_region,
                    "env": config_env,
                }
            })
            self._config_map = response.get("payload", {}).get("configMap", {})
            log.debug("ConfigEnv: Config loaded successfully")
        except Exception as error:
            log.error("ConfigEnv: Failed to load config:", error)
            raise

    def get(self, key: str) -> Any:
        if not self._initialized:
            raise RuntimeError("ConfigEnv not initialized. Call get_instance() and await init() first.")
        return self._config_map.get(key, os.getenv(key))

    def get_all(self) -> Dict[str, Any]:
        if not self._initialized:
            raise RuntimeError("ConfigEnv not initialized. Call get_instance() and await init() first.")
        return self._config_map


# Backward compatibility function
async def init_config() -> Optional[Dict[str, Any]]:
    config_env = ConfigEnv.get_instance()
    await config_env.init()
    return config_env.get_all()
