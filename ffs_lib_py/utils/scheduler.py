import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
import threading
import schedule
from . import log
from .caching import handle_cache_update
from .ffs_http import FFSHttpClient
import aiohttp
from ffs_lib_py.config.config_env import ConfigEnv
import aiohttp
import sys

# Track initialized schedulers
initialized_keys = set()

async def fetch_and_update_cache(key: str, config: Dict[str, Any]) -> None:
    if sys.is_finalizing():
        log.warn("FlagScheduler - Skipping cache update: interpreter is shutting down")
        return

    ce = ConfigEnv.get_instance()
    ffs_config = {
        "authorize_url": ce.get("PASSPORT_SERVICE_URL") + "/api/authorize",
        "base_url": ce.get("FEATURE_FLAG_SERVICE_URL"),
        "passport_auth_key": ce.get("PASSPORT_AUTH_KEY"),
        "passport_secret_key": ce.get("PASSPORT_AUTH_SECRET"),
        "auth_email": ce.get("PASSPORT_USER_AUTH_EMAIL"),
        "token": None
    }

    if not ffs_config.get("base_url"):
        log.error("FlagScheduler - base_url is missing in config")
        return
    if not ffs_config.get("authorize_url"):
        log.error("FlagScheduler - authorize_url is missing in config")
        return
    if not ffs_config.get("passport_auth_key"):
        log.error("FlagScheduler - passport_auth_key is missing in config")
        return
    if not ffs_config.get("passport_secret_key"):
        log.error("FlagScheduler - passport_secret_key is missing in config")
        return
    if not ffs_config.get("auth_email"):
        log.error("FlagScheduler - auth_email is missing in config")
        return

    ffs_http_client = FFSHttpClient(**ffs_config)

    try:
        await ffs_http_client.authorize()
    except Exception:
        log.error("FlagScheduler - issue in authorize call", exc_info=True)
    finally:
         await ffs_http_client.close()    

    current_token = ffs_http_client.token
    flag_names = config.get("flag_names", [])
    if not current_token:
        log.error("FlagScheduler - Token is missing in config")
        return

    all_flags: List[Dict[str, Any]] = []
    offset: Optional[int] = 0
    retried = False
    is_flags_fetched_successfully = True
    try:
        async with aiohttp.ClientSession() as session:
            while offset is not None:
                if sys.is_finalizing():
                    log.warn("FlagScheduler - Interpreter is shutting down. Exiting loop.")
                    break
                try:
                    url = f"{config['baseUrl']}/api/paginated-flags?offset={offset}"
                    headers = {
                        "Authorization": f"Bearer {current_token}",
                        "Content-Type": "application/json"
                    }
                    body = {
                        "flagNames": flag_names
                    }
                    async with session.post(url, json=body, headers=headers, timeout=15) as response:
                        if response.status >= 400:
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status,
                                message=await response.text(),
                                headers=response.headers,
                            )

                        data = await response.json()
                        selected_flags = data.get("payload")
                        meta = data.get("_meta", {})
                        last_returned_index = meta.get("lastReturnedIndex")
                        total_flag_count = meta.get("totalFlagCount")

                        if not isinstance(selected_flags, list):
                            log.warn(f"FlagScheduler - Unexpected flag data format at offset {offset}")
                            is_flags_fetched_successfully = False
                            break

                        if last_returned_index is None or total_flag_count is None:
                            is_flags_fetched_successfully = False
                            log.warn(f"FlagScheduler - Missing pagination metadata at offset {offset}")
                            break

                        log.info(f"Flags fetched {len(selected_flags)} from index {offset} to {last_returned_index - 1}")
                        all_flags.extend(selected_flags)
                        if last_returned_index >= total_flag_count:
                            break
                        offset = last_returned_index

                except aiohttp.ClientResponseError as error:
                    if error.status == 401 and not retried:
                        log.warn("FlagScheduler - Received 401, attempting token refresh...")
                        try:
                            await ffs_http_client.authorize()
                            new_token = ffs_http_client.token
                            if new_token:
                                current_token = new_token
                                retried = True
                                continue
                            else:
                                log.error("FlagScheduler - Token refresh failed - exiting fetch loop")
                                is_flags_fetched_successfully = False
                                break
                        except Exception:
                            log.error("FlagScheduler - Token refresh threw an exception", exc_info=True)
                            is_flags_fetched_successfully = False
                            break
                        finally:
                            await ffs_http_client.close()
                    log.error(f"Failed at offset: {offset} status: {error.status} message: {error.message}", exc_info=True)
                    is_flags_fetched_successfully = False
                    break

        if len(all_flags) and is_flags_fetched_successfully:
            log.info(f"FlagScheduler - Total flags fetched: {len(all_flags)}")
            handle_cache_update(all_flags)
        else:
            log.info(f"FlagScheduler - Skipped cache update: No flags fetched for key {key}")

    except Exception:
        log.error("FlagScheduler - Error fetching flag data", exc_info=True)


def run_fetch_task(key: str, config: Dict[str, Any]) -> None:
    """Run the fetch task in an event loop."""
    try:
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Run the fetch task
        loop.run_until_complete(fetch_and_update_cache(key, config))
        
        # Close the loop
        loop.close()
    except Exception as e:
        log.error(f"Error in scheduler thread: {str(e)}")

async def start_scheduler(key: str, interval_in_minutes: int = 1, config: Dict[str, Any] = None) -> None:
    """Start a scheduler to periodically fetch flags."""
    if not key:
        log.error("Missing key in flag cache scheduler")
        return
    
    # Check if scheduler already exists
    if key in initialized_keys:
        log.warn(f"Scheduler already exists for key: {key}. Skipping re-initialization.")
        return
    
    # Add to initialized keys
    initialized_keys.add(key)
    
    # Run initial fetch
    # threading.Thread(target=run_fetch_task, args=(key, config)).start()
    await fetch_and_update_cache(key, config)
    # Schedule periodic fetch
    schedule.every(interval_in_minutes).minutes.do(
        lambda: threading.Thread(target=run_fetch_task, args=(key, config)).start()
    )
    
    # Start scheduler thread if not already running
    if not hasattr(start_scheduler, "scheduler_thread") or not start_scheduler.scheduler_thread.is_alive():
        start_scheduler.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        start_scheduler.scheduler_thread.start()
    
    log.info(f"Flag cache scheduler started :: key '{key}' :: (every {interval_in_minutes} minutes)")

def run_scheduler() -> None:
    """Run the scheduler in a loop."""
    while True:
        schedule.run_pending()
        time.sleep(1)
