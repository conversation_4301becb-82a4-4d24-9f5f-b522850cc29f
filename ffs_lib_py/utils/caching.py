import time
import hashlib
import json
from typing import Dict, Any, List, Optional
from .helper import evaluate_flags, MASTER_FLAG_HASH, FLAG_HASH_DATA, LRU_CACHE_SIZE
from . import log
from cachetools import LRUCache

# LRU Cache Setup
_lru_cache = LRUCache(maxsize=LRU_CACHE_SIZE)

# Simple in-memory cache
_cache: Dict[str, Any] = {}
_cache_ttl: Dict[str, float] = {}

def create_hash(data: Any) -> str:
    if isinstance(data, dict):
        data = {k: data[k] for k in sorted(data.keys())}
    data_str = json.dumps(data, sort_keys=True)
    return hashlib.sha256(data_str.encode()).hexdigest()

def set_data_in_cache(key: str, value: Any) -> None:
    _cache[key] = value

def set_data_in_cache_with_ttl(key: str, value: Any, ttl: int) -> None:
    _cache[key] = value
    _cache_ttl[key] = time.time() + ttl

def get_cache(key: str) -> Any:
    if key not in _cache:
        return None
    if key in _cache_ttl and time.time() > _cache_ttl[key]:
        del _cache[key]
        del _cache_ttl[key]
        return None
    return _cache[key]

def check_if_key_exists(key: str) -> bool:
    return key in _cache

def check_for_cache_renewal(new_hash: str) -> bool:
    existing_hash = get_cache(MASTER_FLAG_HASH)
    return existing_hash != new_hash

def get_all_flags_from_cache() -> List[Dict[str, Any]]:
    hash_map = get_cache(FLAG_HASH_DATA) or {}
    return [get_cache(flag_key) for flag_key in hash_map if get_cache(flag_key) is not None]

def clear_flag_cache():
    hash_map = get_cache(FLAG_HASH_DATA) or {}
    keys_to_delete = list(hash_map.keys()) + [MASTER_FLAG_HASH, FLAG_HASH_DATA]
    for key in keys_to_delete:
        _cache.pop(key, None)
        _cache_ttl.pop(key, None)

# LRU Cache Functions
def set_lru_cache(key: str, value: Any) -> None:
    _lru_cache[key] = value

def get_lru_cache(key: str) -> Optional[Any]:
    return _lru_cache.get(key)

def lru_cache_key_exists(key: str) -> bool:
    return key in _lru_cache

def delete_lru_cache_key(key: str) -> None:
    _lru_cache.pop(key, None)

def get_all_lru_cache_keys() -> list:
    return list(_lru_cache.keys())

def handle_cache_update(all_flags: List[Dict[str, Any]]):
    master_hash = create_hash(all_flags)
    should_initialize = not check_if_key_exists(MASTER_FLAG_HASH)

    if not should_initialize and not check_for_cache_renewal(master_hash):
        log.info("No changes detected in flags. Skipping update.")
        return

    old_hash_map = get_cache(FLAG_HASH_DATA) or {}
    new_hash_map: Dict[str, str] = {}
    flags_to_update: List[Dict[str, Any]] = []
    updated_flags: List[str] = []
    remaining_old_keys = set(old_hash_map.keys())

    for flag in all_flags:
        name = flag.get("name")
        if not name:
            continue
        key = f"FLAG:{name}"
        flag_hash = create_hash(flag)
        new_hash_map[key] = flag_hash

        if should_initialize:
            flags_to_update.append({"key": key, "val": flag})
        elif old_hash_map.get(key) != flag_hash:
            updated_flags.append(key)
            set_data_in_cache(key, flag)
            log.info(f"Updated Cache - Updated {name} {time.ctime()}")
        remaining_old_keys.discard(key)

    deleted_flags = list(remaining_old_keys)
    if should_initialize:
        for entry in flags_to_update:
            set_data_in_cache(entry["key"], entry["val"])
        log.info(f"Initialized Cache - {len(all_flags)} flags initialized :: {time.ctime()}")
    elif deleted_flags:
        for key in deleted_flags:
            _cache.pop(key, None)
        log.info(f"Updated Cache - Deleted {len(deleted_flags)} flags :: {time.ctime()}")

    set_data_in_cache(FLAG_HASH_DATA, new_hash_map)
    set_data_in_cache(MASTER_FLAG_HASH, master_hash)
    update_context_cache(updated_flags, deleted_flags)

def update_context_cache(updated_flags: List[str], deleted_flags: List[str]):
    if len(updated_flags) < 1 and len(deleted_flags) < 1:
        return
    log.info(f"Updating Context - Updated {len(updated_flags)}, Deleted {len(deleted_flags)} :: {time.ctime()}")
    context_map = get_all_lru_cache_keys()
    if not context_map:
        log.info("No contexts found to update.")
        return
    
    log.info(f"Contexts length - {len(context_map)}")
    for key in context_map:
        updated_flag_objs = [get_cache(k) for k in updated_flags if get_cache(k)]
        context_data = get_lru_cache(key)
        context = context_data.get("context", {})
        old_result = context_data.get("result", {})
        updated_result = evaluate_flags(updated_flag_objs, context)
        result = {**old_result, **updated_result}
        for deleted_key in deleted_flags:
            result.pop(deleted_key, None)
        set_lru_cache(key, {"masterFlagHash": get_cache(MASTER_FLAG_HASH),"context": context, "result": result})
        log.info(f"Updated Context - Context for key: {key} updated")

"""
──────────────────────────────────────────────────────────────────────────────
CACHE KEY STRUCTURE & USAGE OVERVIEW
──────────────────────────────────────────────────────────────────────────────

MASTER_FLAG_HASH
  - Type: str
  - Purpose: Single sha256 hash representing the entire flag data.
  - Usage: Detects if any flag data has changed since last fetch.
  - Example:
      'MASTER_FLAG_HASH': 'sha256_hash_of_all_flags_data'

FLAG_HASH_DATA
  - Type: Dict[str, str]
  - Purpose: Maps flag names to their individual sha256 hashes.
  - Usage: Detects if a specific flag has changed.
  - Note: All keys are prefixed with 'FLAG:' to avoid conflicts.
  - Example:
      'FLAG_HASH_DATA': {
          'FLAG:flagname1': 'sha256_hash_of_flag_1_obj',
          'FLAG:flagname2': 'sha256_hash_of_flag_2_obj'
      }

Individual Flag Storage:
  - Each flag is stored with its name as the key, prefixed by 'FLAG:'.
  - Example:
      'FLAG:flagname1': { 'name': 'flagname1', 'active': 1, 'flagRoles': [...] }

Individual Context Evaluation Result Storage:
  - Each evaluated context result is stored with its hashed context as the key, prefixed by 'CONTEXT:'.
  - Only this layer uses the LRU cache for temporary and efficient context-level caching.
  - Example:
      'CONTEXT:hashed_context_1': {
          'masterFlagHash': 'sha256_hash_of_all_flags_data',
          'context': { 'userId': 123, 'botId': 456, ... },
          'result': { 'flagname1': True, 'flagname2': False, ... }
      }
──────────────────────────────────────────────────────────────────────────────
"""

