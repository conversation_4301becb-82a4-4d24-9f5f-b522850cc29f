import hashlib
import json
import logging
from typing import Dict, Any, List, Optional

def create_hash(data: Any) -> str:
    """Create a hash from any data object."""
    if isinstance(data, dict):
        # Sort keys for consistent hashing
        data = {k: data[k] for k in sorted(data.keys())}
    
    data_str = json.dumps(data, sort_keys=True)
    return hashlib.md5(data_str.encode()).hexdigest()

def evaluate_flags(flags: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
    current_user_id = context.get("userId")
    current_user_email = context.get("email")
    current_user_org_id = context.get("orgId")
    current_service_name = context.get("serviceName")
    current_bot_id = context.get("botId")
    current_bot_ref_id = context.get("botRefId")
    current_env = context.get("env")

    result: Dict[str, Any] = {}
    order_of_precedence = ['userEmail', 'userId', 'botId', 'botRefId', 'serviceName', 'env', 'orgId']

    for flag in flags:
        flag_name = flag.get("name")
        if not flag_name:
            continue

        value = None
        value_for_all_rules: Dict[str, Any] = {}
        matches = 0

        # If flag is off
        if flag.get("active") == 0:
            off_value = (flag.get("offValue") or {}).get("value")
            if off_value == "true":
                result[flag_name] = True
            elif off_value == "false":
                result[flag_name] = False
            else:
                result[flag_name] = off_value if off_value is not None else None
            continue

        # Default value
        value = (flag.get("defaultValue") or {}).get("value")

        # Role-based overrides
        for role in flag.get("flagRoles", []) or []:
            if role.get("active") == 0:
                continue

            on_value = (role.get("onValue") or {}).get("value")
            user = role.get("user") or {}

            if current_user_email and user.get("email") == current_user_email:
                value_for_all_rules["userEmail"] = on_value
                matches += 1

            if current_user_id and user.get("id") == current_user_id:
                value_for_all_rules["userId"] = on_value
                matches += 1

            if current_user_org_id and role.get("orgId") == current_user_org_id:
                value_for_all_rules["orgId"] = on_value
                matches += 1

            role_bot_id = role.get("botId")
            role_bot_ref_id = role.get("botRefId")

            if role_bot_ref_id:
                if (
                    current_bot_id == role_bot_id and
                    current_bot_ref_id == role_bot_ref_id
                ):
                    value_for_all_rules["botId"] = on_value
                    matches += 1
            elif current_bot_id == role_bot_id:
                value_for_all_rules["botId"] = on_value
                matches += 1

            if current_bot_ref_id and current_bot_ref_id == role_bot_ref_id:
                value_for_all_rules["botRefId"] = on_value
                matches += 1

            if current_service_name and current_service_name == role.get("serviceName"):
                value_for_all_rules["serviceName"] = on_value
                matches += 1

            if current_env and current_env == role.get("env"):
                value_for_all_rules["env"] = on_value
                matches += 1

        # Pick the value based on precedence
        if matches:
            for key in order_of_precedence:
                if key in value_for_all_rules and value_for_all_rules[key] is not None:
                    value = value_for_all_rules[key]
                    break

        # Try to parse value if it's JSON
        if isinstance(value, str):
            try:
                if value.startswith("{") or value.startswith("["):
                    value = json.loads(value)
            except Exception:
                pass

        # Convert string 'true'/'false' to boolean
        if value == "true":
            value = True
        elif value == "false":
            value = False

        result[flag_name] = value

    return result

def get_current_datetime():
    """Get current datetime string."""
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# Constants
MASTER_FLAG_HASH = "MASTER_FLAG_HASH"
FLAG_HASH_DATA = "FLAG_HASH_DATA"
LRU_CACHE_SIZE = 200