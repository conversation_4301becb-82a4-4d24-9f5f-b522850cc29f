import os
import traceback
from typing import Any
from time import perf_counter

_timers = {}

def _get_pid():
    return f":[process id: {os.getpid()}]"

def debug(message: str, *args: Any) -> None:
    print(f"[FeatureFlagSDK] DEBUG: {message}", *args, _get_pid())

def info(message: str, *args: Any) -> None:
    print(f"[FeatureFlagSDK] INFO: {message}", *args, _get_pid())

def warn(message: str, *args: Any) -> None:
    print(f"[FeatureFlagSDK] WARN: {message}", *args, _get_pid())

def error(message: str, *args: Any, exc_info: bool = False) -> None:
    print(f"[FeatureFlagSDK] ERROR: {message}", *args, _get_pid())
    if exc_info:
        traceback.print_exc()

def enter(message: str, *args: Any) -> None:
    print(f"[FeatureFlagSDK] ENTER: {message}", *args, _get_pid())

def exit(message: str, *args: Any) -> None:
    print(f"[FeatureFlagSDK] EXIT: {message}", *args, _get_pid())

def enterx(service: str, method: str, message: str = "", *args: Any) -> None:
    key = f"{service}:{method}"
    print(f"[FeatureFlagSDK] ENTER: {key} {message}", *args)
    _timers[key] = perf_counter()

def exitx(service: str, method: str, message: str = "", *args: Any) -> None:
    key = f"{service}:{method}"
    elapsed = perf_counter() - _timers.get(key, perf_counter())
    print(f"[FeatureFlagSDK] EXIT: {key} {message}", *args, f"[{elapsed:.2f}s]")
    _timers.pop(key, None)

def errorx(service: str, method: str, message: str, *args: Any) -> None:
    key = f"{service}:{method}"
    elapsed = perf_counter() - _timers.get(key, perf_counter())
    print(f"[FeatureFlagSDK] ERROR: {key} {message}", *args, f"[{elapsed:.2f}s]")
    _timers.pop(key, None)

