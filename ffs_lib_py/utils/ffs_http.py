# ffs_http_client.py

import json
import logging
import aiohttp
import sys
from typing import Dict, Any, Optional
from . import log

class FFSHttpClient:
    def __init__(
        self,
        base_url:  Optional[str] = None,
        authorize_url:  Optional[str] = None,
        passport_auth_key:  Optional[str] = None,
        passport_secret_key:  Optional[str] = None,
        auth_email:  Optional[str] = None,
        token: Optional[str] = None
    ):
        self.base_url = base_url
        self.authorize_url = authorize_url
        self.passport_auth_key = passport_auth_key
        self.passport_secret_key = passport_secret_key
        self.auth_email = auth_email
        self.token = token
        self.session = None

    async def _ensure_session(self):
        if self.session is None and not sys.is_finalizing():
            self.session = aiohttp.ClientSession()
        return self.session

    async def authorize(self) -> bool:
        if sys.is_finalizing():
            return False

        if not self.authorize_url or not self.passport_auth_key or not self.passport_secret_key:
            log.error("Missing authorization configuration")
            return False
        
        try:
            session = await self._ensure_session()
            if not session:
                return False

            async with session.post(
                self.authorize_url,
                json={"email": self.auth_email},
                headers={
                    "x-passport-key": self.passport_auth_key,
                    "x-passport-secret": self.passport_secret_key,
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    log.error(f"Authorization failed: {error_text}")
                    return False

                result = await response.json()
                self.token = result.get("payload", {}).get("token")

                if not self.token:
                    log.error("No token received from authorization endpoint")
                    return False

                return True

        except Exception as e:
            if not sys.is_finalizing():
                log.error(f"Authorization error: {str(e)}")
            return False

    async def get(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        if sys.is_finalizing():
            return None

        url = params.get("url", "")
        if not url:
            log.error("URL is required for GET request")
            return None

        if not self.token and not await self.authorize():
            return None

        try:
            full_url = f"{self.base_url}/{url}"
            session = await self._ensure_session()
            if not session:
                return None

            async with session.get(
                full_url,
                headers={
                    "Authorization": f"Bearer {self.token}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    log.error(f"GET request failed: {error_text}")

                    if response.status == 401 and await self.authorize():
                        return await self.get(params)
                    
                    return None

                return await response.json()

        except Exception as e:
            if not sys.is_finalizing():
                log.error(f"GET request error: {str(e)}")
            return None

    async def post(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        if sys.is_finalizing():
            return None

        url = params.get("url", "")
        body = params.get("body", {})

        if not self.base_url:
            log.error("FFSHttpClient: base_url is required for POST request")
            return None

        full_url = f"{self.base_url}/{url.lstrip('/')}"
        headers = {
            "Content-Type": "application/json"
        }

        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(full_url, json=body, headers=headers) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        log.error(f"FFSHttpClient: POST request failed ({response.status}): {error_text}")

                        if response.status == 401 and await self.authorize():
                            # Retry once after re-auth
                            return await self.post(params)

                        return None

                    return await response.json()

        except Exception as e:
            if not sys.is_finalizing():
                log.error(f"FFSHttpClient: POST request error: {str(e)}")
            return None

    async def close(self):
        if self.session and not sys.is_finalizing():
            await self.session.close()
            self.session = None
