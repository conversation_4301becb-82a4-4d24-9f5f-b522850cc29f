from typing import Dict, Any, List, Callable, TypedDict

class FlagRole(TypedDict, total=False):
    botId: str
    botRefId: str
    orgId: str
    serviceName: str
    env: str

class Flag(TypedDict, total=False):
    id: str
    name: str
    description: str
    enabled: bool
    flagRoles: List[FlagRole]
    targeting: List[Dict[str, Any]]
    createdAt: str
    updatedAt: str

class ApiResponse(TypedDict, total=False):
    success: bool
    message: str
    payload: Any
    _meta: Dict[str, Any]

# Type for flag listeners
FlagListener = Callable[[str, Any], None]
FlagListeners = Dict[str, FlagListener]