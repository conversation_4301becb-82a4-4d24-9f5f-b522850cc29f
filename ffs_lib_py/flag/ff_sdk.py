import json
import asyncio
from typing import Dict, Any, Optional, List
import aiohttp
from ffs_lib_py.config.config_env import ConfigEnv
from ffs_lib_py.utils.ffs_http import FFSHttpClient
from ffs_lib_py.utils.caching import (
    get_cache, get_all_flags_from_cache, create_hash, get_lru_cache, set_lru_cache
)
from ffs_lib_py.utils.helper import (
    MASTER_FLAG_HASH, evaluate_flags
)
from ffs_lib_py.utils.scheduler import start_scheduler
from ffs_lib_py.utils import log

class FeatureFlagSDK:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FeatureFlagSDK, cls).__new__(cls)
            # Initialize instance variables
            cls._instance._initialize_instance()
        return cls._instance
    
    def _initialize_instance(self):
        """Initialize instance variables."""
        # Initialization state tracking
        self.initialized = False
        self.initializing = None
        
        # Instance variables
        self.ffs_http_client = None
        self.ffs_config = None
    
    @classmethod
    def get_instance(cls):
        return cls()
    
    def init_sync(self, interval_in_minutes: int = 1, flag_names: Optional[List[str]] = None):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(self.init(interval_in_minutes, flag_names))

    async def init(self, interval_in_minutes: int = 1, flag_names: Optional[List[str]] = None) -> 'FeatureFlagSDK':
        """Initialize the Feature Flag SDK."""
        if flag_names is None:
            flag_names = []
        # If already initialized, return the instance immediately
        if self.initialized:
            return self
            
        # If initialization is in progress, wait for it to complete
        if self.initializing:
            await self.initializing
            return self
            
        # Get ConfigEnv instance and initialize it
        ce = ConfigEnv.get_instance()
        await ce.init()
        log.info(f"FeatureFlagSDK: ConfigEnv initialized")
        
        # Get token from passport service
        self.ffs_config = {
            "authorizeUrl": f"{ce.get('PASSPORT_SERVICE_URL')}/api/authorize",
            "baseUrl": ce.get("FEATURE_FLAG_SERVICE_URL"),
            "passportAuthKey": ce.get("PASSPORT_AUTH_KEY"),
            "passportSecretKey": ce.get("PASSPORT_AUTH_SECRET"),
            "authEmail": ce.get("PASSPORT_USER_AUTH_EMAIL")
        }
        
        if not all(self.ffs_config.values()):
            log.error("Missing passport configuration")
            raise ValueError("Missing passport configuration")
            
        # Create initialization future
        initialization_future = asyncio.get_event_loop().create_future()
        self.initializing = initialization_future
        
        try:
            # Get token from passport service
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ffs_config['authorizeUrl']}",
                    json={"email": self.ffs_config['authEmail']},
                    headers={
                        "x-passport-key": self.ffs_config['passportAuthKey'],
                        "x-passport-secret": self.ffs_config['passportSecretKey'],
                        "Content-Type": "application/json"
                    }
                ) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        raise ValueError(f"Failed to get token: {error_text}")
                        
                    result = await response.json()
                    token = result.get("payload", {}).get("token")
                    
            if not token:
                raise ValueError("Failed to get token from passport service")
            self.ffs_config["token"] = token
            self.ffs_config["flag_names"] = flag_names
                
            # Initialize with token
            await self._initialize(interval_in_minutes)
            
            # Mark as initialized
            self.initialized = True
            initialization_future.set_result(self)
            
            return self
        except Exception as e:
            initialization_future.set_exception(e)
            raise
        finally:
            self.initializing = None
    
    async def _initialize(self, interval_in_minutes: int) -> None:
        """Private method to perform actual initialization."""
        log.info("init - Starting initialization")
        if not self.ffs_config.get("token"):
            log.error("init - Token is missing in config")
            raise ValueError("Token is missing in config")
        if not self.ffs_config.get("baseUrl"):
            log.error("init - baseUrl is missing in config")
            raise ValueError("baseUrl is missing in config")
        if not self.ffs_config.get("authorizeUrl"):
            log.error("init - authorizeUrl is missing in config")
            raise ValueError("authorizeUrl is missing in config")
        if not self.ffs_config.get("passportAuthKey"):
            log.error("init - passportAuthKey is missing in config")
            raise ValueError("passportAuthKey is missing in config")
        if not self.ffs_config.get("passportSecretKey"):
            log.error("init - passportSecretKey is missing in config")
            raise ValueError("passportSecretKey is missing in config")
        if not self.ffs_config.get("authEmail"):
            log.error("init - authEmail is missing in config")
            raise ValueError("authEmail is missing in config")
        # Initialize HTTP client
        self.ffs_http_client = FFSHttpClient(
            base_url=self.ffs_config["baseUrl"],
            authorize_url=self.ffs_config["authorizeUrl"],
            passport_auth_key=self.ffs_config["passportAuthKey"],
            passport_secret_key=self.ffs_config["passportSecretKey"],
            auth_email=self.ffs_config["authEmail"],
            token=self.ffs_config["token"]
        )
        
        
        # Start scheduler
        await self._start_scheduler(interval_in_minutes)
        
        # Reset listeners
        self.listeners = {}
            
        log.info("FeatureFlagSDK: Initialization complete")

    
    async def _start_scheduler(self, interval_in_minutes: int) -> None:
        """Start the flag cache scheduler."""
        if not self.ffs_config or not self.ffs_config["token"]:
            log.error("Cannot start scheduler: token is missing")
            return
            
        await start_scheduler(
            key="feature-flag-cache",
            interval_in_minutes=interval_in_minutes,
            config=self.ffs_config
        )

    def get_flag_value(self, flag_name:str, context: Dict[str, Any]) -> Any:
        """Get flag value for a context."""  
        if not flag_name:
            log.error("get_flag_value: Missing required argument 'flag_name")
            return None
        all_flags = self.get_flags(context)
        return all_flags.get(flag_name)

    def get_flags(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get flags for a context."""
        # Create hash from context
        context_hash = f"context:{create_hash(context or {})}"

        # Get master flag hash
        master_flag_hash = get_cache(MASTER_FLAG_HASH)
        if not master_flag_hash:
            log.error("getFlags - Master flag hash is not set. Please initialize the FeatureFlagSDK first.")
            return {}

        
        # Check if we have cached result for this context
        context_cache = get_lru_cache(context_hash)
        if context_cache and context_cache.get("masterFlagHash") == master_flag_hash:
            log.info("getFlags - Returning cached data")
            return context_cache.get("result", {})
        
        try:
            # Get all flags from cache
            all_flags = get_all_flags_from_cache()
                
            # Evaluate flags against context
            result = evaluate_flags(all_flags or [], context)
            
            # Cache the result
            set_lru_cache(context_hash, {"masterFlagHash": master_flag_hash, "context": context, "result": result})
            
            log.info(f"getFlags - Created evaluated flags cache for context: {context_hash}")
            return result
            
        except Exception as e:
            log.error(f"Failed to get flags for context: {json.dumps(context)}", e)
            return {}

    