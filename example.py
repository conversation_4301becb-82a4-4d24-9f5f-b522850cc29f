# main.py

import asyncio
from ffs_lib_py.flag.ff_sdk import FeatureFlagSDK
import json

ffs_client = None

async def get_ffs_client():
    global ffs_client
    if not ffs_client:
        sdk_instance = FeatureFlagSDK.get_instance()
        ffs_client = await sdk_instance.init(interval_in_minutes=1)
    return ffs_client

async def fetchFlags():
    client = await get_ffs_client()
    context = {
        "email": "<EMAIL>",
        "botId": "919b2f03-f114-4a83-81c6-e408b254d130"
    }
    result = client.get_flags(context)
    print("result:", json.dumps(result, indent=2))

async def main():
    await fetchFlags()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("🛑 Shutdown requested. Exiting gracefully...")
    except asyncio.CancelledError:
        print("🛑 Async task was cancelled.")
    except Exception as e:
        print("❌ Unhandled error:", e)