# ffs-lib-py

A robust and efficient Feature Flag Service Library for Python applications. It offers built-in caching, scheduled data synchronization, and easy integration.

---

## Features

- Seamless integration with any Python application using a plug-and-play approach.
- Efficient in-memory caching mechanism with automated flag synchronization at scheduled intervals.
- High-performance architecture leveraging local cache to reduce latency and minimize API calls.
- Support for LaunchDarkly integration.

---

## Installation

```bash
pip install ffs-lib-py
```

---

## Quick Start

```python
from ffs_lib_py import FeatureFlagSDK

# Initialize the SDK
ff_sdk = FeatureFlagSDK.get_instance()
await ff_sdk.init(interval_in_minutes=2)

# Get flags for a context
flags = await ff_sdk.get_flags({"user_id": "123", "org_id": "456"})
```

---

## Environment Variables

Create a `.env` file in your project root:

```env
CONFIG_ENV=config_env
CONFIG_REGION=config_region
CONFIG_HOST=config_host_url
```

---

## Library Usage

The `FeatureFlagSDK` exposes a set of intuitive methods to manage and retrieve feature flags efficiently within your application.

### 1. `get_instance()`

Returns a singleton instance of the `FeatureFlagSDK`.

```python
ff_sdk = FeatureFlagSDK.get_instance()
```

Use this to ensure a single SDK instance is used throughout your application.

---

### 2. `async init(interval_in_minutes=1, flag_names=None)`

Initializes the SDK instance and sets up the internal caching and scheduled refresh interval (in minutes). Must be called **once** before using other methods.

```python
await ff_sdk.init(interval_in_minutes=2, flag_names=['FFS_NETOMI', 'FFS_BETA_FEATURE'])  # Optional
```

- `interval_in_minutes`: *(int, optional)* — How often to refresh flags from the server. Default is `1`.
- `flag_names`: *(list of str, optional)* — If provided, only these flags will be fetched and cached.
- Ensures flag data is kept up to date.
- Use `flag_names` to limit the flags fetched to only those required by your application.

---

### 3. `init_sync(interval_in_minutes=1, flag_names=None)`

Synchronous version of the `init()` method. Initializes the SDK in a blocking manner (no `await` needed).

```python
ff_sdk.init_sync(interval_in_minutes=2, flag_names=['FFS_NETOMI', 'FFS_BETA_FEATURE'])  # All optional
```

- `interval_in_minutes`: *(int, optional)* — How often to refresh flags from the server. Default is `1`.
- `flag_names`: *(list of str, optional)* — If provided, only these flags will be fetched and cached.
- Useful when using the SDK in non-async environments.
- Should only be used if you're not using an async event loop.
- Initializes the SDK instance and sets up internal caching and scheduled refresh interval.
- Must be called **once** before using other methods.

---

### 4. `get_flags(context)`

Fetches **all feature flags** for a given context.

```python
flags = ff_sdk.get_flags({
    "bot_id": "some-bot-id",
    "email": "<EMAIL>"
})
```

- All context fields are optional.
- If no context is provided, default values will be used based on your flag configuration.

---

### 4. `get_flag_value(flag_name, context)`

Fetches the **value of a specific flag** based on the context provided.

```python
value = ff_sdk.get_flag_value("FFS_NETOMI", {
    "user_id": 4,
    "email": "<EMAIL>"
})
```

- Returns the evaluated flag value based on matching context.

---

## Best Practices

### Initialization

- Call `init()` as early as possible (e.g., during application startup).
- Ensure `init()` is only called once.
- Implement error handling and retries if initialization fails.

### Scheduling

- Use an interval (in minutes) that reflects how frequently your flag configuration changes.
- Avoid too frequent refreshes unless necessary.

---

## Dependencies

- `schedule`
- `aiohttp`
- `setuptools`
- `dotenv`

---