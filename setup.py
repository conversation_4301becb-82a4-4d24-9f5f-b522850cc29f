from setuptools import setup, find_packages

with open("README.md", "r") as fh:
    long_description = fh.read()

setup(
    name="ffs-lib-py",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "aiohttp>=3.8.0",
        "python-dotenv>=0.19.0",
        "schedule>=1.1.0",
        "setuptools>=65.0.0",
        "cachetools>=5.0.0",
    ],
    author="msgai",
    author_email="<EMAIL>",
    long_description=long_description,
    long_description_content_type="text/markdown", 
    description="Feature Flag Service Library for Python",
    keywords="feature-flag, ffs-lib-py",
    url="https://github.com/msgai/ffs-lib-py.git",
    classifiers=[
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
    ],
    python_requires=">=3.8",
)